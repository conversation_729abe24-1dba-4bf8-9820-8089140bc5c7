package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.AppQuestionBank;
import com.ruoyi.system.service.IAppQuestionBankService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 题库管理Controller
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@RestController
@RequestMapping("/system/questionBank")
public class AppQuestionBankController extends BaseController
{
    @Autowired
    private IAppQuestionBankService appQuestionBankService;

    /**
     * 查询题库管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:questionBank:list')")
    @GetMapping("/list")
    public TableDataInfo list(AppQuestionBank appQuestionBank)
    {
        startPage();
        List<AppQuestionBank> list = appQuestionBankService.selectAppQuestionBankList(appQuestionBank);
        return getDataTable(list);
    }

    /**
     * 导出题库管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:questionBank:export')")
    @Log(title = "题库管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppQuestionBank appQuestionBank)
    {
        List<AppQuestionBank> list = appQuestionBankService.selectAppQuestionBankList(appQuestionBank);
        ExcelUtil<AppQuestionBank> util = new ExcelUtil<AppQuestionBank>(AppQuestionBank.class);
        util.exportExcel(response, list, "题库管理数据");
    }

    /**
     * 获取题库管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:questionBank:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(appQuestionBankService.selectAppQuestionBankById(id));
    }

    /**
     * 新增题库管理
     */
    @PreAuthorize("@ss.hasPermi('system:questionBank:add')")
    @Log(title = "题库管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppQuestionBank appQuestionBank)
    {
        return toAjax(appQuestionBankService.insertAppQuestionBank(appQuestionBank));
    }

    /**
     * 修改题库管理
     */
    @PreAuthorize("@ss.hasPermi('system:questionBank:edit')")
    @Log(title = "题库管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppQuestionBank appQuestionBank)
    {
        return toAjax(appQuestionBankService.updateAppQuestionBank(appQuestionBank));
    }

    /**
     * 删除题库管理
     */
    @PreAuthorize("@ss.hasPermi('system:questionBank:remove')")
    @Log(title = "题库管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(appQuestionBankService.deleteAppQuestionBankByIds(ids));
    }
}
