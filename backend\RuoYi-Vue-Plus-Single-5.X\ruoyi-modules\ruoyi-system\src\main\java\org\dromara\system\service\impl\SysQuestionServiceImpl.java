package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.AppQuestionMapper;
import com.ruoyi.system.domain.AppQuestion;
import com.ruoyi.system.service.IAppQuestionService;

/**
 * 题目Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@Service
public class AppQuestionServiceImpl implements IAppQuestionService 
{
    @Autowired
    private AppQuestionMapper appQuestionMapper;

    /**
     * 查询题目
     * 
     * @param id 题目主键
     * @return 题目
     */
    @Override
    public AppQuestion selectAppQuestionById(Long id)
    {
        return appQuestionMapper.selectAppQuestionById(id);
    }

    /**
     * 查询题目列表
     * 
     * @param appQuestion 题目
     * @return 题目
     */
    @Override
    public List<AppQuestion> selectAppQuestionList(AppQuestion appQuestion)
    {
        return appQuestionMapper.selectAppQuestionList(appQuestion);
    }

    /**
     * 新增题目
     * 
     * @param appQuestion 题目
     * @return 结果
     */
    @Override
    public int insertAppQuestion(AppQuestion appQuestion)
    {
        appQuestion.setCreateTime(DateUtils.getNowDate());
        return appQuestionMapper.insertAppQuestion(appQuestion);
    }

    /**
     * 修改题目
     * 
     * @param appQuestion 题目
     * @return 结果
     */
    @Override
    public int updateAppQuestion(AppQuestion appQuestion)
    {
        appQuestion.setUpdateTime(DateUtils.getNowDate());
        return appQuestionMapper.updateAppQuestion(appQuestion);
    }

    /**
     * 批量删除题目
     * 
     * @param ids 需要删除的题目主键
     * @return 结果
     */
    @Override
    public int deleteAppQuestionByIds(Long[] ids)
    {
        return appQuestionMapper.deleteAppQuestionByIds(ids);
    }

    /**
     * 删除题目信息
     * 
     * @param id 题目主键
     * @return 结果
     */
    @Override
    public int deleteAppQuestionById(Long id)
    {
        return appQuestionMapper.deleteAppQuestionById(id);
    }
}
