package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 题目对象 app_question
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public class AppQuestion extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 题目ID */
    private Long id;

    /** 题库ID */
    @Excel(name = "题库ID")
    private Long questionBankId;

    /** 题目标题 */
    @Excel(name = "题目标题")
    private String title;

    /** 题目内容 */
    private String content;

    /** 题目类型(single/multiple/judge/essay/code) */
    @Excel(name = "题目类型(single/multiple/judge/essay/code)")
    private String type;

    /** 难度等级(easy/medium/hard) */
    @Excel(name = "难度等级(easy/medium/hard)")
    private String difficulty;

    /** 选项(JSON格式) */
    private String options;

    /** 正确答案 */
    private String correctAnswer;

    /** 答案解析 */
    private String explanation;

    /** 标签(逗号分隔) */
    @Excel(name = "标签(逗号分隔)")
    private String tags;

    /** 练习次数 */
    @Excel(name = "练习次数")
    private Long practiceCount;

    /** 正确次数 */
    @Excel(name = "正确次数")
    private Long correctCount;

    /** 评论数量 */
    @Excel(name = "评论数量")
    private Long commentCount;

    /** 状态(0正常 1停用) */
    @Excel(name = "状态(0正常 1停用)")
    private String status;

    /** 排序 */
    private Long sortOrder;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setQuestionBankId(Long questionBankId) 
    {
        this.questionBankId = questionBankId;
    }

    public Long getQuestionBankId() 
    {
        return questionBankId;
    }
    public void setTitle(String title) 
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }
    public void setContent(String content) 
    {
        this.content = content;
    }

    public String getContent() 
    {
        return content;
    }
    public void setType(String type) 
    {
        this.type = type;
    }

    public String getType() 
    {
        return type;
    }
    public void setDifficulty(String difficulty) 
    {
        this.difficulty = difficulty;
    }

    public String getDifficulty() 
    {
        return difficulty;
    }
    public void setOptions(String options) 
    {
        this.options = options;
    }

    public String getOptions() 
    {
        return options;
    }
    public void setCorrectAnswer(String correctAnswer) 
    {
        this.correctAnswer = correctAnswer;
    }

    public String getCorrectAnswer() 
    {
        return correctAnswer;
    }
    public void setExplanation(String explanation) 
    {
        this.explanation = explanation;
    }

    public String getExplanation() 
    {
        return explanation;
    }
    public void setTags(String tags) 
    {
        this.tags = tags;
    }

    public String getTags() 
    {
        return tags;
    }
    public void setPracticeCount(Long practiceCount) 
    {
        this.practiceCount = practiceCount;
    }

    public Long getPracticeCount() 
    {
        return practiceCount;
    }
    public void setCorrectCount(Long correctCount) 
    {
        this.correctCount = correctCount;
    }

    public Long getCorrectCount() 
    {
        return correctCount;
    }
    public void setCommentCount(Long commentCount) 
    {
        this.commentCount = commentCount;
    }

    public Long getCommentCount() 
    {
        return commentCount;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setSortOrder(Long sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Long getSortOrder() 
    {
        return sortOrder;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("questionBankId", getQuestionBankId())
            .append("title", getTitle())
            .append("content", getContent())
            .append("type", getType())
            .append("difficulty", getDifficulty())
            .append("options", getOptions())
            .append("correctAnswer", getCorrectAnswer())
            .append("explanation", getExplanation())
            .append("tags", getTags())
            .append("practiceCount", getPracticeCount())
            .append("correctCount", getCorrectCount())
            .append("commentCount", getCommentCount())
            .append("status", getStatus())
            .append("sortOrder", getSortOrder())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
