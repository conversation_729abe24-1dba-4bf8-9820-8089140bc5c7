{"doc": "\n 题库管理Service接口\r\n \r\n <AUTHOR>\r\n @date 2025-08-02\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectAppQuestionBankById", "paramTypes": ["java.lang.Long"], "doc": "\n 查询题库管理\r\n \r\n @param id 题库管理主键\r\n @return 题库管理\r\n"}, {"name": "selectAppQuestionBankList", "paramTypes": ["AppQuestionBank"], "doc": "\n 查询题库管理列表\r\n \r\n @param appQuestionBank 题库管理\r\n @return 题库管理集合\r\n"}, {"name": "insertAppQuestionBank", "paramTypes": ["AppQuestionBank"], "doc": "\n 新增题库管理\r\n \r\n @param appQuestionBank 题库管理\r\n @return 结果\r\n"}, {"name": "updateAppQuestionBank", "paramTypes": ["AppQuestionBank"], "doc": "\n 修改题库管理\r\n \r\n @param appQuestionBank 题库管理\r\n @return 结果\r\n"}, {"name": "deleteAppQuestionBankByIds", "paramTypes": ["java.lang.Long[]"], "doc": "\n 批量删除题库管理\r\n \r\n @param ids 需要删除的题库管理主键集合\r\n @return 结果\r\n"}, {"name": "deleteAppQuestionBankById", "paramTypes": ["java.lang.Long"], "doc": "\n 删除题库管理信息\r\n \r\n @param id 题库管理主键\r\n @return 结果\r\n"}], "constructors": []}