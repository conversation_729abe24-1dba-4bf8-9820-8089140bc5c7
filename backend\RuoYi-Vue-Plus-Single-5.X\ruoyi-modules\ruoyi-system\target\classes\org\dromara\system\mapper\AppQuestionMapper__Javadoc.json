{"doc": " 题目Mapper接口\n \n <AUTHOR>\n @date 2025-08-02\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectAppQuestionById", "paramTypes": ["java.lang.Long"], "doc": " 查询题目\n \n @param id 题目主键\n @return 题目\n"}, {"name": "selectAppQuestionList", "paramTypes": ["AppQuestion"], "doc": " 查询题目列表\n \n @param appQuestion 题目\n @return 题目集合\n"}, {"name": "insertAppQuestion", "paramTypes": ["AppQuestion"], "doc": " 新增题目\n \n @param appQuestion 题目\n @return 结果\n"}, {"name": "updateAppQuestion", "paramTypes": ["AppQuestion"], "doc": " 修改题目\n \n @param appQuestion 题目\n @return 结果\n"}, {"name": "deleteAppQuestionById", "paramTypes": ["java.lang.Long"], "doc": " 删除题目\n \n @param id 题目主键\n @return 结果\n"}, {"name": "deleteAppQuestionByIds", "paramTypes": ["java.lang.Long[]"], "doc": " 批量删除题目\n \n @param ids 需要删除的数据主键集合\n @return 结果\n"}], "constructors": []}