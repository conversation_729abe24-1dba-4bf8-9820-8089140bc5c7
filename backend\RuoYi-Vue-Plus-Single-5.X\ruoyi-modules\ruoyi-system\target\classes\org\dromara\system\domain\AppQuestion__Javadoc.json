{"doc": "\n 题目对象 app_question\r\n \r\n <AUTHOR>\r\n @date 2025-08-02\r\n", "fields": [{"name": "id", "doc": "题目ID "}, {"name": "questionBankId", "doc": "题库ID "}, {"name": "title", "doc": "题目标题 "}, {"name": "content", "doc": "题目内容 "}, {"name": "type", "doc": "题目类型(single/multiple/judge/essay/code) "}, {"name": "difficulty", "doc": "难度等级(easy/medium/hard) "}, {"name": "options", "doc": "选项(JSON格式) "}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "doc": "正确答案 "}, {"name": "explanation", "doc": "答案解析 "}, {"name": "tags", "doc": "标签(逗号分隔) "}, {"name": "practiceCount", "doc": "练习次数 "}, {"name": "correctCount", "doc": "正确次数 "}, {"name": "commentCount", "doc": "评论数量 "}, {"name": "status", "doc": "状态(0正常 1停用) "}, {"name": "sortOrder", "doc": "排序 "}], "enumConstants": [], "methods": [], "constructors": []}