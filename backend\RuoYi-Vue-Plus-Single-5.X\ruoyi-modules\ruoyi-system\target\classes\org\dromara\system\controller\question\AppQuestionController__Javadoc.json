{"doc": " 题目Controller\n \n <AUTHOR>\n @date 2025-08-02\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["AppQuestion"], "doc": " 查询题目列表\n"}, {"name": "export", "paramTypes": ["HttpServletResponse", "AppQuestion"], "doc": " 导出题目列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取题目详细信息\n"}, {"name": "add", "paramTypes": ["AppQuestion"], "doc": " 新增题目\n"}, {"name": "edit", "paramTypes": ["AppQuestion"], "doc": " 修改题目\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除题目\n"}], "constructors": []}