{"doc": "\n 题目Service接口\r\n \r\n <AUTHOR>\r\n @date 2025-08-02\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectAppQuestionById", "paramTypes": ["java.lang.Long"], "doc": "\n 查询题目\r\n \r\n @param id 题目主键\r\n @return 题目\r\n"}, {"name": "selectAppQuestionList", "paramTypes": ["AppQuestion"], "doc": "\n 查询题目列表\r\n \r\n @param appQuestion 题目\r\n @return 题目集合\r\n"}, {"name": "insertAppQuestion", "paramTypes": ["AppQuestion"], "doc": "\n 新增题目\r\n \r\n @param appQuestion 题目\r\n @return 结果\r\n"}, {"name": "updateAppQuestion", "paramTypes": ["AppQuestion"], "doc": "\n 修改题目\r\n \r\n @param appQuestion 题目\r\n @return 结果\r\n"}, {"name": "deleteAppQuestionByIds", "paramTypes": ["java.lang.Long[]"], "doc": "\n 批量删除题目\r\n \r\n @param ids 需要删除的题目主键集合\r\n @return 结果\r\n"}, {"name": "deleteAppQuestionById", "paramTypes": ["java.lang.Long"], "doc": "\n 删除题目信息\r\n \r\n @param id 题目主键\r\n @return 结果\r\n"}], "constructors": []}