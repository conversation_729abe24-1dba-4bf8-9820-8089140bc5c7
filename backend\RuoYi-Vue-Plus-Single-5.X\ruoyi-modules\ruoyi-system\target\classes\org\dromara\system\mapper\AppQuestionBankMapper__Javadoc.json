{"doc": " 题库管理Mapper接口\n \n <AUTHOR>\n @date 2025-08-02\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectAppQuestionBankById", "paramTypes": ["java.lang.Long"], "doc": " 查询题库管理\n \n @param id 题库管理主键\n @return 题库管理\n"}, {"name": "selectAppQuestionBankList", "paramTypes": ["AppQuestionBank"], "doc": " 查询题库管理列表\n \n @param appQuestionBank 题库管理\n @return 题库管理集合\n"}, {"name": "insertAppQuestionBank", "paramTypes": ["AppQuestionBank"], "doc": " 新增题库管理\n \n @param appQuestionBank 题库管理\n @return 结果\n"}, {"name": "updateAppQuestionBank", "paramTypes": ["AppQuestionBank"], "doc": " 修改题库管理\n \n @param appQuestionBank 题库管理\n @return 结果\n"}, {"name": "deleteAppQuestionBankById", "paramTypes": ["java.lang.Long"], "doc": " 删除题库管理\n \n @param id 题库管理主键\n @return 结果\n"}, {"name": "deleteAppQuestionBankByIds", "paramTypes": ["java.lang.Long[]"], "doc": " 批量删除题库管理\n \n @param ids 需要删除的数据主键集合\n @return 结果\n"}], "constructors": []}