package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.AppQuestionBankMapper;
import com.ruoyi.system.domain.AppQuestionBank;
import com.ruoyi.system.service.IAppQuestionBankService;

/**
 * 题库管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@Service
public class AppQuestionBankServiceImpl implements IAppQuestionBankService 
{
    @Autowired
    private AppQuestionBankMapper appQuestionBankMapper;

    /**
     * 查询题库管理
     * 
     * @param id 题库管理主键
     * @return 题库管理
     */
    @Override
    public AppQuestionBank selectAppQuestionBankById(Long id)
    {
        return appQuestionBankMapper.selectAppQuestionBankById(id);
    }

    /**
     * 查询题库管理列表
     * 
     * @param appQuestionBank 题库管理
     * @return 题库管理
     */
    @Override
    public List<AppQuestionBank> selectAppQuestionBankList(AppQuestionBank appQuestionBank)
    {
        return appQuestionBankMapper.selectAppQuestionBankList(appQuestionBank);
    }

    /**
     * 新增题库管理
     * 
     * @param appQuestionBank 题库管理
     * @return 结果
     */
    @Override
    public int insertAppQuestionBank(AppQuestionBank appQuestionBank)
    {
        appQuestionBank.setCreateTime(DateUtils.getNowDate());
        return appQuestionBankMapper.insertAppQuestionBank(appQuestionBank);
    }

    /**
     * 修改题库管理
     * 
     * @param appQuestionBank 题库管理
     * @return 结果
     */
    @Override
    public int updateAppQuestionBank(AppQuestionBank appQuestionBank)
    {
        appQuestionBank.setUpdateTime(DateUtils.getNowDate());
        return appQuestionBankMapper.updateAppQuestionBank(appQuestionBank);
    }

    /**
     * 批量删除题库管理
     * 
     * @param ids 需要删除的题库管理主键
     * @return 结果
     */
    @Override
    public int deleteAppQuestionBankByIds(Long[] ids)
    {
        return appQuestionBankMapper.deleteAppQuestionBankByIds(ids);
    }

    /**
     * 删除题库管理信息
     * 
     * @param id 题库管理主键
     * @return 结果
     */
    @Override
    public int deleteAppQuestionBankById(Long id)
    {
        return appQuestionBankMapper.deleteAppQuestionBankById(id);
    }
}
