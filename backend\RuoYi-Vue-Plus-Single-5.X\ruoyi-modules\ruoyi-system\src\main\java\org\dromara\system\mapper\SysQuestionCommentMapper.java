package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.AppQuestionComment;

/**
 * 题目评论Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface AppQuestionCommentMapper 
{
    /**
     * 查询题目评论
     * 
     * @param id 题目评论主键
     * @return 题目评论
     */
    public AppQuestionComment selectAppQuestionCommentById(Long id);

    /**
     * 查询题目评论列表
     * 
     * @param appQuestionComment 题目评论
     * @return 题目评论集合
     */
    public List<AppQuestionComment> selectAppQuestionCommentList(AppQuestionComment appQuestionComment);

    /**
     * 新增题目评论
     * 
     * @param appQuestionComment 题目评论
     * @return 结果
     */
    public int insertAppQuestionComment(AppQuestionComment appQuestionComment);

    /**
     * 修改题目评论
     * 
     * @param appQuestionComment 题目评论
     * @return 结果
     */
    public int updateAppQuestionComment(AppQuestionComment appQuestionComment);

    /**
     * 删除题目评论
     * 
     * @param id 题目评论主键
     * @return 结果
     */
    public int deleteAppQuestionCommentById(Long id);

    /**
     * 批量删除题目评论
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAppQuestionCommentByIds(Long[] ids);
}
