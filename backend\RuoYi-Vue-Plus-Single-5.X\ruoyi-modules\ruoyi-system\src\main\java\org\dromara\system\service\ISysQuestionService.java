package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.AppQuestion;

/**
 * 题目Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface IAppQuestionService 
{
    /**
     * 查询题目
     * 
     * @param id 题目主键
     * @return 题目
     */
    public AppQuestion selectAppQuestionById(Long id);

    /**
     * 查询题目列表
     * 
     * @param appQuestion 题目
     * @return 题目集合
     */
    public List<AppQuestion> selectAppQuestionList(AppQuestion appQuestion);

    /**
     * 新增题目
     * 
     * @param appQuestion 题目
     * @return 结果
     */
    public int insertAppQuestion(AppQuestion appQuestion);

    /**
     * 修改题目
     * 
     * @param appQuestion 题目
     * @return 结果
     */
    public int updateAppQuestion(AppQuestion appQuestion);

    /**
     * 批量删除题目
     * 
     * @param ids 需要删除的题目主键集合
     * @return 结果
     */
    public int deleteAppQuestionByIds(Long[] ids);

    /**
     * 删除题目信息
     * 
     * @param id 题目主键
     * @return 结果
     */
    public int deleteAppQuestionById(Long id);
}
