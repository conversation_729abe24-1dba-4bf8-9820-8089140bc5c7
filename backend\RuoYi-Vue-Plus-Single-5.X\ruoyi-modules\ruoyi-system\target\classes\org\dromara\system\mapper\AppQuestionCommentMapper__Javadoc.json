{"doc": " 题目评论Mapper接口\n \n <AUTHOR>\n @date 2025-08-02\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectAppQuestionCommentById", "paramTypes": ["java.lang.Long"], "doc": " 查询题目评论\n \n @param id 题目评论主键\n @return 题目评论\n"}, {"name": "selectAppQuestionCommentList", "paramTypes": ["AppQuestionComment"], "doc": " 查询题目评论列表\n \n @param appQuestionComment 题目评论\n @return 题目评论集合\n"}, {"name": "insertAppQuestionComment", "paramTypes": ["AppQuestionComment"], "doc": " 新增题目评论\n \n @param appQuestionComment 题目评论\n @return 结果\n"}, {"name": "updateAppQuestionComment", "paramTypes": ["AppQuestionComment"], "doc": " 修改题目评论\n \n @param appQuestionComment 题目评论\n @return 结果\n"}, {"name": "deleteAppQuestionCommentById", "paramTypes": ["java.lang.Long"], "doc": " 删除题目评论\n \n @param id 题目评论主键\n @return 结果\n"}, {"name": "deleteAppQuestionCommentByIds", "paramTypes": ["java.lang.Long[]"], "doc": " 批量删除题目评论\n \n @param ids 需要删除的数据主键集合\n @return 结果\n"}], "constructors": []}