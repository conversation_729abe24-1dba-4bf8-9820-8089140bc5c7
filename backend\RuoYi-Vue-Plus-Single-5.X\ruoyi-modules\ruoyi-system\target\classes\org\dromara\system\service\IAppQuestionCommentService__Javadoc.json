{"doc": "\n 题目评论Service接口\r\n \r\n <AUTHOR>\r\n @date 2025-08-02\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectAppQuestionCommentById", "paramTypes": ["java.lang.Long"], "doc": "\n 查询题目评论\r\n \r\n @param id 题目评论主键\r\n @return 题目评论\r\n"}, {"name": "selectAppQuestionCommentList", "paramTypes": ["AppQuestionComment"], "doc": "\n 查询题目评论列表\r\n \r\n @param appQuestionComment 题目评论\r\n @return 题目评论集合\r\n"}, {"name": "insertAppQuestionComment", "paramTypes": ["AppQuestionComment"], "doc": "\n 新增题目评论\r\n \r\n @param appQuestionComment 题目评论\r\n @return 结果\r\n"}, {"name": "updateAppQuestionComment", "paramTypes": ["AppQuestionComment"], "doc": "\n 修改题目评论\r\n \r\n @param appQuestionComment 题目评论\r\n @return 结果\r\n"}, {"name": "deleteAppQuestionCommentByIds", "paramTypes": ["java.lang.Long[]"], "doc": "\n 批量删除题目评论\r\n \r\n @param ids 需要删除的题目评论主键集合\r\n @return 结果\r\n"}, {"name": "deleteAppQuestionCommentById", "paramTypes": ["java.lang.Long"], "doc": "\n 删除题目评论信息\r\n \r\n @param id 题目评论主键\r\n @return 结果\r\n"}], "constructors": []}