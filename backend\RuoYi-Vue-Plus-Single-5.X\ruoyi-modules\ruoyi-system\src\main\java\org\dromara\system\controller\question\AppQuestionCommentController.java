package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.AppQuestionComment;
import com.ruoyi.system.service.IAppQuestionCommentService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 题目评论Controller
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@RestController
@RequestMapping("/system/questionComment")
public class AppQuestionCommentController extends BaseController
{
    @Autowired
    private IAppQuestionCommentService appQuestionCommentService;

    /**
     * 查询题目评论列表
     */
    @PreAuthorize("@ss.hasPermi('system:questionComment:list')")
    @GetMapping("/list")
    public TableDataInfo list(AppQuestionComment appQuestionComment)
    {
        startPage();
        List<AppQuestionComment> list = appQuestionCommentService.selectAppQuestionCommentList(appQuestionComment);
        return getDataTable(list);
    }

    /**
     * 导出题目评论列表
     */
    @PreAuthorize("@ss.hasPermi('system:questionComment:export')")
    @Log(title = "题目评论", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppQuestionComment appQuestionComment)
    {
        List<AppQuestionComment> list = appQuestionCommentService.selectAppQuestionCommentList(appQuestionComment);
        ExcelUtil<AppQuestionComment> util = new ExcelUtil<AppQuestionComment>(AppQuestionComment.class);
        util.exportExcel(response, list, "题目评论数据");
    }

    /**
     * 获取题目评论详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:questionComment:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(appQuestionCommentService.selectAppQuestionCommentById(id));
    }

    /**
     * 新增题目评论
     */
    @PreAuthorize("@ss.hasPermi('system:questionComment:add')")
    @Log(title = "题目评论", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppQuestionComment appQuestionComment)
    {
        return toAjax(appQuestionCommentService.insertAppQuestionComment(appQuestionComment));
    }

    /**
     * 修改题目评论
     */
    @PreAuthorize("@ss.hasPermi('system:questionComment:edit')")
    @Log(title = "题目评论", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppQuestionComment appQuestionComment)
    {
        return toAjax(appQuestionCommentService.updateAppQuestionComment(appQuestionComment));
    }

    /**
     * 删除题目评论
     */
    @PreAuthorize("@ss.hasPermi('system:questionComment:remove')")
    @Log(title = "题目评论", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(appQuestionCommentService.deleteAppQuestionCommentByIds(ids));
    }
}
