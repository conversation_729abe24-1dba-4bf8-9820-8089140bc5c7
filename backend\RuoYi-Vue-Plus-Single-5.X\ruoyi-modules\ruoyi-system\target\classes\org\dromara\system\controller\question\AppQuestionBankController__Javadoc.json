{"doc": " 题库管理Controller\n \n <AUTHOR>\n @date 2025-08-02\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["AppQuestionBank"], "doc": " 查询题库管理列表\n"}, {"name": "export", "paramTypes": ["HttpServletResponse", "AppQuestionBank"], "doc": " 导出题库管理列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取题库管理详细信息\n"}, {"name": "add", "paramTypes": ["AppQuestionBank"], "doc": " 新增题库管理\n"}, {"name": "edit", "paramTypes": ["AppQuestionBank"], "doc": " 修改题库管理\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除题库管理\n"}], "constructors": []}