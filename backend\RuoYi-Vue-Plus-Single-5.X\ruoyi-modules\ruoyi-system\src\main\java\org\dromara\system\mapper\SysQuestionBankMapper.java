package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.AppQuestionBank;

/**
 * 题库管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface AppQuestionBankMapper 
{
    /**
     * 查询题库管理
     * 
     * @param id 题库管理主键
     * @return 题库管理
     */
    public AppQuestionBank selectAppQuestionBankById(Long id);

    /**
     * 查询题库管理列表
     * 
     * @param appQuestionBank 题库管理
     * @return 题库管理集合
     */
    public List<AppQuestionBank> selectAppQuestionBankList(AppQuestionBank appQuestionBank);

    /**
     * 新增题库管理
     * 
     * @param appQuestionBank 题库管理
     * @return 结果
     */
    public int insertAppQuestionBank(AppQuestionBank appQuestionBank);

    /**
     * 修改题库管理
     * 
     * @param appQuestionBank 题库管理
     * @return 结果
     */
    public int updateAppQuestionBank(AppQuestionBank appQuestionBank);

    /**
     * 删除题库管理
     * 
     * @param id 题库管理主键
     * @return 结果
     */
    public int deleteAppQuestionBankById(Long id);

    /**
     * 批量删除题库管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAppQuestionBankByIds(Long[] ids);
}
