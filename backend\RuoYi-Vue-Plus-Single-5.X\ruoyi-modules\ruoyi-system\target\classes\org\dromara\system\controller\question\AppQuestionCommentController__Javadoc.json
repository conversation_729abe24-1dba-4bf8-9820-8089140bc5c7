{"doc": " 题目评论Controller\n \n <AUTHOR>\n @date 2025-08-02\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["AppQuestionComment"], "doc": " 查询题目评论列表\n"}, {"name": "export", "paramTypes": ["HttpServletResponse", "AppQuestionComment"], "doc": " 导出题目评论列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取题目评论详细信息\n"}, {"name": "add", "paramTypes": ["AppQuestionComment"], "doc": " 新增题目评论\n"}, {"name": "edit", "paramTypes": ["AppQuestionComment"], "doc": " 修改题目评论\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除题目评论\n"}], "constructors": []}