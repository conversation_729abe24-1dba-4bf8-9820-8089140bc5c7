package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 题库管理对象 app_question_bank
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public class AppQuestionBank extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 题库ID */
    private Long id;

    /** 题库标题 */
    @Excel(name = "题库标题")
    private String title;

    /** 题库描述 */
    private String description;

    /** 题库图标 */
    @Excel(name = "题库图标")
    private String icon;

    /** 题库颜色 */
    @Excel(name = "题库颜色")
    private String color;

    /** 难度等级(easy/medium/hard) */
    @Excel(name = "难度等级(easy/medium/hard)")
    private String difficulty;

    /** 题目总数 */
    @Excel(name = "题目总数")
    private Long totalQuestions;

    /** 练习次数 */
    @Excel(name = "练习次数")
    private Long practiceCount;

    /** 专业ID */
    @Excel(name = "专业ID")
    private Long majorId;

    /** 状态(0正常 1停用) */
    @Excel(name = "状态(0正常 1停用)")
    private String status;

    /** 排序 */
    @Excel(name = "排序")
    private Long sortOrder;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTitle(String title) 
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }
    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }
    public void setIcon(String icon) 
    {
        this.icon = icon;
    }

    public String getIcon() 
    {
        return icon;
    }
    public void setColor(String color) 
    {
        this.color = color;
    }

    public String getColor() 
    {
        return color;
    }
    public void setDifficulty(String difficulty) 
    {
        this.difficulty = difficulty;
    }

    public String getDifficulty() 
    {
        return difficulty;
    }
    public void setTotalQuestions(Long totalQuestions) 
    {
        this.totalQuestions = totalQuestions;
    }

    public Long getTotalQuestions() 
    {
        return totalQuestions;
    }
    public void setPracticeCount(Long practiceCount) 
    {
        this.practiceCount = practiceCount;
    }

    public Long getPracticeCount() 
    {
        return practiceCount;
    }
    public void setMajorId(Long majorId) 
    {
        this.majorId = majorId;
    }

    public Long getMajorId() 
    {
        return majorId;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setSortOrder(Long sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Long getSortOrder() 
    {
        return sortOrder;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("title", getTitle())
            .append("description", getDescription())
            .append("icon", getIcon())
            .append("color", getColor())
            .append("difficulty", getDifficulty())
            .append("totalQuestions", getTotalQuestions())
            .append("practiceCount", getPracticeCount())
            .append("majorId", getMajorId())
            .append("status", getStatus())
            .append("sortOrder", getSortOrder())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
