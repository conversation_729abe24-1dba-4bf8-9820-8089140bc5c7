package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.AppQuestionCommentMapper;
import com.ruoyi.system.domain.AppQuestionComment;
import com.ruoyi.system.service.IAppQuestionCommentService;

/**
 * 题目评论Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@Service
public class AppQuestionCommentServiceImpl implements IAppQuestionCommentService 
{
    @Autowired
    private AppQuestionCommentMapper appQuestionCommentMapper;

    /**
     * 查询题目评论
     * 
     * @param id 题目评论主键
     * @return 题目评论
     */
    @Override
    public AppQuestionComment selectAppQuestionCommentById(Long id)
    {
        return appQuestionCommentMapper.selectAppQuestionCommentById(id);
    }

    /**
     * 查询题目评论列表
     * 
     * @param appQuestionComment 题目评论
     * @return 题目评论
     */
    @Override
    public List<AppQuestionComment> selectAppQuestionCommentList(AppQuestionComment appQuestionComment)
    {
        return appQuestionCommentMapper.selectAppQuestionCommentList(appQuestionComment);
    }

    /**
     * 新增题目评论
     * 
     * @param appQuestionComment 题目评论
     * @return 结果
     */
    @Override
    public int insertAppQuestionComment(AppQuestionComment appQuestionComment)
    {
        appQuestionComment.setCreateTime(DateUtils.getNowDate());
        return appQuestionCommentMapper.insertAppQuestionComment(appQuestionComment);
    }

    /**
     * 修改题目评论
     * 
     * @param appQuestionComment 题目评论
     * @return 结果
     */
    @Override
    public int updateAppQuestionComment(AppQuestionComment appQuestionComment)
    {
        appQuestionComment.setUpdateTime(DateUtils.getNowDate());
        return appQuestionCommentMapper.updateAppQuestionComment(appQuestionComment);
    }

    /**
     * 批量删除题目评论
     * 
     * @param ids 需要删除的题目评论主键
     * @return 结果
     */
    @Override
    public int deleteAppQuestionCommentByIds(Long[] ids)
    {
        return appQuestionCommentMapper.deleteAppQuestionCommentByIds(ids);
    }

    /**
     * 删除题目评论信息
     * 
     * @param id 题目评论主键
     * @return 结果
     */
    @Override
    public int deleteAppQuestionCommentById(Long id)
    {
        return appQuestionCommentMapper.deleteAppQuestionCommentById(id);
    }
}
