package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.AppQuestion;
import com.ruoyi.system.service.IAppQuestionService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 题目Controller
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@RestController
@RequestMapping("/system/question")
public class AppQuestionController extends BaseController
{
    @Autowired
    private IAppQuestionService appQuestionService;

    /**
     * 查询题目列表
     */
    @PreAuthorize("@ss.hasPermi('system:question:list')")
    @GetMapping("/list")
    public TableDataInfo list(AppQuestion appQuestion)
    {
        startPage();
        List<AppQuestion> list = appQuestionService.selectAppQuestionList(appQuestion);
        return getDataTable(list);
    }

    /**
     * 导出题目列表
     */
    @PreAuthorize("@ss.hasPermi('system:question:export')")
    @Log(title = "题目", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppQuestion appQuestion)
    {
        List<AppQuestion> list = appQuestionService.selectAppQuestionList(appQuestion);
        ExcelUtil<AppQuestion> util = new ExcelUtil<AppQuestion>(AppQuestion.class);
        util.exportExcel(response, list, "题目数据");
    }

    /**
     * 获取题目详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:question:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(appQuestionService.selectAppQuestionById(id));
    }

    /**
     * 新增题目
     */
    @PreAuthorize("@ss.hasPermi('system:question:add')")
    @Log(title = "题目", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppQuestion appQuestion)
    {
        return toAjax(appQuestionService.insertAppQuestion(appQuestion));
    }

    /**
     * 修改题目
     */
    @PreAuthorize("@ss.hasPermi('system:question:edit')")
    @Log(title = "题目", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppQuestion appQuestion)
    {
        return toAjax(appQuestionService.updateAppQuestion(appQuestion));
    }

    /**
     * 删除题目
     */
    @PreAuthorize("@ss.hasPermi('system:question:remove')")
    @Log(title = "题目", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(appQuestionService.deleteAppQuestionByIds(ids));
    }
}
